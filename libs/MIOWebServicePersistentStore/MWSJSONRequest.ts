import { MWSRequest } from "./MWSRequest";
import { <PERSON><PERSON>og } from "../MIOFoundation";

export class MWS<PERSON><PERSON><PERSON>equest extends MWSRequest 
{    
    protected willStart() {        
        this.setHeaderValue("application/json", "Content-Type");
        
        if (this.body != null) {
            this.bodyData = JSON.stringify(this.body);
        }
    }
    
    protected didFinish(){
        if (this.resultData != null && this.resultData != "") {
            try {
                this.resultData = JSON.parse(this.resultData.replace(/(\r\n|\n|\r)/gm, ""));
            } catch (error) {
                MIOLog("JSON PARSER ERROR: BODY -> " + this.bodyData);
                MIOLog("JSON PARSER ERROR: RESULT -> " + this.resultData);
            }
            
        }
    }
}