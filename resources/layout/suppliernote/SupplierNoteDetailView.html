<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>SupplierNoteDetailView</title>
    <link href="../../styles.css" rel="stylesheet" />
</head>

<body>
    <!-- Page -->
    <div id="supplier-note-detail-view" class="page">

        <!--toolbar-->
        <div class="toolbar">
            <div class="left"></div>
            <div class="center">
                <div class="title">
                    <span data-localized-key="SUPPLIER_NOTE">SUPPLIER NOTE</span>
                </div>
            </div>
            <div class="right">
                <div class="btn dropdown hidden" data-outlet="select-category"><span data-localized-key="SELECT_CATEGORY">SELECT CATEGORY</span></div>
                <div class="btn dropdown hidden" data-outlet="select-tab"><span data-localized-key="SELECT_TAG">SELECT TAG</span></div>
                <div class="btn" data-outlet="all-received-btn"><span data-localized-key="ALL_RECEIVED">ALL RECEIVED</span></div>
                <div class="btn save" data-outlet="save-btn"><span data-localized-key="SAVE">SAVE</span></div>                
                <div class="btn print" data-outlet="print-btn"></div>
                <div class="btn settings" data-outlet="action-btn"></div>
            </div>
        </div>

       
        <!--header-container-->
        <div class="header-container" data-outlet="no-edit-header-view">
            <div class="box">
                <div class="row-wide">
                    <div class="title align-left"><span data-localized-key="DOCUMENT_NUMBER">DOCUMENT NUMBER</span></div>
                    <div class="label" data-outlet="document-number-lbl"><span>DOCUMENT NUMBER</span></div>  
                </div>
                <div class="row-wide">
                    <div class="title align-left"><span data-localized-key="FROM_DOCUMENT_NUMBER">FROM DOCUMENT NUMBER</span></div>
                    <div class="label" data-outlet="parent-document-number-lbl"><span>PARENT DOCUMENT NUMBER</span></div>
                </div>
            </div>
            <div class="box">
                <div class="row-wide">
                    <div class="title align-left"><span data-localized-key="SUPPLIER">SUPPLIER</span></div>
                    <div class="label" data-outlet="origin-lbl"><span>Label</span></div>
                </div>
                <div class="row-wide">
                    <div class="title"><span data-localized-key="REFERENCE">REFERENCE</span></div>
                    <div class="label" data-outlet="external-reference-lbl"><span>Label</span></div>
                </div>
            </div>
            <div class="box">
                <div class="title"><span data-localized-key="DOCUMENT_DATE">DOCUMENT DATE</span></div>
                <div class="label" data-outlet="document-date-lbl"><span>DD/MM/YYYY</span></div>
            </div>
            <div class="box">
                <div class="title"><span data-localized-key="VALIDATION_DATE">VALIDATION DATE</span></div>
                <div class="label" data-outlet="stock-date-lbl"><span>DD/MM/YYYY</span></div>
            </div>
            <div class="box">
                <div class="blank"></div>
                <div class="btn alert" data-outlet="change-status-btn"><span data-localized-key="PROCESSED">PROCESSED</span></div>
            </div>
        </div>

        <!--header-container-->
        <div class="header-container" data-outlet="edit-header-view">
            <div class="box">
                <div class="row-wide">
                    <div class="title align-left"><span data-localized-key="DOCUMENT_NUMBER">DOCUMENT NUMBER</span></div>
                    <div class="label" data-outlet="document-number-lbl"><span>DOCUMENT NUMBER</span></div>  
                </div>
                <div class="row-wide">
                    <div class="title align-left"><span data-localized-key="FROM_DOCUMENT_NUMBER">FROM DOCUMENT NUMBER</span></div>
                    <div class="label" data-outlet="parent-document-number-lbl"><span>PARENT DOCUMENT NUMBER</span></div>
                </div>
            </div>
            <div class="box">
                <div class="row-wide">
                    <div class="title align-left"><span data-localized-key="SUPPLIER">SUPPLIER</span></div>
                    <div class="label" data-outlet="origin-lbl"><span>Label</span></div>
                </div>
                <div class="row-wide">
                    <div class="title"><span data-localized-key="REFERENCE">REFERENCE</span></div>
                    <div class="input-text" data-outlet="external-reference-tf">
                        <input type="text" placeholder="REFERENCE" data-placeholder="REFERENCE">
                    </div>
                </div>
            </div>
            <div class="box">
                    <div class="title"><span data-localized-key="DOCUMENT_DATE">DOCUMENT DATE</span></div>
                <div class="input-btn" data-outlet="document-date-tf">
                    <input type="text" placeholder="DD/MM/YYYY" data-placeholder="DD/MM/YYYY">
                    <div class="btn calendar" data-outlet="document-date-btn"></div>
                </div>
            </div>
            <div class="box">
                <div class="title"><span data-localized-key="VALIDATION_DATE">VALIDATION DATE</span></div>
                <div class="input-btn" data-outlet="stock-date-tf">
                    <input type="text" placeholder="DD/MM/YYYY">
                    <div class="btn calendar" data-outlet="stock-date-btn"></div>
                </div>
            </div>
            <div class="box">
                <div class="blank"></div>
                <div class="btn" data-outlet="change-status-btn"><span data-localized-key="PROCESS">PROCESS</span></div>
            </div>
        </div>

        <div class="header-container secondary-container">
            <div class="box">
                <div class="title"><span data-localized-key="COMMENTS">COMMENTS</span></div>
                <div class="input-text align-left" data-outlet="comments-tf">
                    <input type="text" placeholder="COMMENTS" data-placeholder="COMMENTS">
                </div>
            </div>
            <div class="box">
                <div class="title"><span data-localized-key="WAREHOUSE">WAREHOUSE</span></div>
                <div class="row align-right">
                    <div class="label" data-outlet="warehouse-lbl"><span>Warehouse</span></div>
                    <div class="btn edit" data-outlet="warehouse-btn"></div>
                </div>
            </div>
            <div class="box">
                <div class="title"><span data-localized-key="BASE">BASE</span></div>
                <div class="label" data-outlet="base-lbl"><span>Label</span></div>
            </div>
            <div class="box">
                <div class="title"><span data-localized-key="TAX">TAX</span></div>
                <div class="label" data-outlet="tax-lbl"><span>Label</span></div>
            </div>
            <div class="box">
                <div class="title"><span data-localized-key="TOTAL">TOTAL</span></div>
                <div class="label strong large" data-outlet="total-lbl"><span>Label</span></div>
            </div>
        </div>

        <!--searchbar-->
        <div class="searchbar" data-outlet="search-bar">
            <input type="search" placeholder="SEARCH" data-placeholder="SEARCH">
        </div>

        <!--titlebar-->
        <div class="titlebar" data-outlet="titlebar">
            <div class="blank"></div>
            <div class="title"><span data-localized-key="CONCEPT">CONCEPT</span></div>            
            <div class="title align-center"><span data-localized-key="ORDERED_QUANTITY">ORDER QUANTITY</span></div>
            <div class="title align-center"><span data-localized-key="INPUT_FORMAT">INPUT FORMAT</span></div>
            <div class="title align-right"><span data-localized-key="QUANTITY">QUANTITY</span></div>
            <div class="title align-right"><span data-localized-key="PRICE">PRICE</span></div>
            <div class="title align-right"><span data-localized-key="PRICEUNIT">PRICE/UNIT</span></div>
            <div class="title align-right"><span data-localized-key="DISCOUNT">DISCOUNT</span></div>
            <div class="title align-right"><span data-localized-key="TOTAL">TOTAL</span></div>
        </div>

        <!--titlebar-->
        <div class="titlebar" data-outlet="titlebar-no-orderquantity">
            <div class="blank"></div>
            <div class="title col-span-2"><span data-localized-key="CONCEPT">CONCEPT</span></div>            
            <div class="title align-center"><span data-localized-key="INPUT_FORMAT">INPUT FORMAT</span></div>
            <div class="title align-right"><span data-localized-key="QUANTITY">QUANTITY</span></div>
            <div class="title align-right"><span data-localized-key="PRICE">PRICE</span></div>
            <div class="title align-right"><span data-localized-key="PRICEUNIT">PRICE/UNIT</span></div>
            <div class="title align-right"><span data-localized-key="DISCOUNT">DISCOUNT</span></div>
            <div class="title align-right"><span data-localized-key="TOTAL">TOTAL</span></div>
        </div>

        <!--content-container-->
        <div class="content-container">
            <!--top add line--> 
            <div class="grid-row add-line" data-outlet="top-add-line" data-cell-identifier="AddLineCell" data-class="SupplierNoteLineCell">
                <div class="btn add white" data-outlet="add-line-btn"></div>
                <div class="input-text align-left col-span-2" data-outlet="product-tf">
                    <input type="text" placeholder="CONCEPT NAME" data-placeholder="CONCEPT NAME">
                </div>      
                <div class="btn dropdown align-center" data-outlet="input-format-dd"><span data-localized-key="INPUT_FORMAT">INPUT FORMAT</span></div>
                <div class="input-text align-right" data-outlet="quantity-tf">
                    <input type="text" placeholder="0" data-placeholder="0">
                </div>
                <div class="input-text align-right" data-outlet="price-tf">
                    <input type="text" placeholder="00.00" data-placeholder="00.00">
                </div>
                <div class="input-text align-right" data-outlet="product-price-tf">
                    <input type="text" placeholder="00.00" data-placeholder="00.00">
                </div>
                <div class="input-text align-right" data-outlet="discount-tf">
                    <input type="text" placeholder="0" data-placeholder="0">
                </div>
                <div class="input-text align-right" data-outlet="total-tf">
                    <input type="text" placeholder="00.00" data-placeholder="00.00">
                </div>
            </div>
        </div>

        <!--table-->
        <div class="uitable" data-outlet="table-view">

            <!--new cell-->
            <div class="cell" data-cell-identifier="EditLineCell" data-class="SupplierNoteLineCell">
                <!--first cell line-->
                <div class="btn remove" data-editing-accessory-view="delete"></div>
                <div class="row">
                    <div class="label" data-outlet="product-lbl">Concept name</div>
                    <div class="btn info-cell" data-outlet="info-icon-btn"></div>
                </div>
                <div class="label align-right" data-outlet="ordered-quantity-lbl"><span>0</span></div>
                <div class="btn dropdown align-center" data-outlet="input-format-dd"><span data-localized-key="INPUT_FORMAT">INPUT FORMAT</span></div>
                <div class="input-text align-right" data-outlet="quantity-tf">
                    <input type="text" placeholder="0" data-placeholder="0">
                </div>
                <div class="input-text align-right" data-outlet="price-tf">
                    <input type="text" placeholder="00.00" data-placeholder="00.00">
                </div>
                <div class="input-text align-right" data-outlet="product-price-tf">
                    <input type="text" placeholder="00.00" data-placeholder="00.00">
                </div>
                <div class="input-text align-right" data-outlet="discount-tf">
                    <input type="text" placeholder="0" data-placeholder="0">
                </div>
                <div class="input-text align-right" data-outlet="total-tf">
                    <input type="text" placeholder="00.00" data-placeholder="00.00">
                </div>
                <!--second cell line-->
                <div class="blank"></div>
                <div class="label light" data-outlet="concept-subtext-lbl">Concept label</div>
                <div class="blank"></div>
                <div class="label align-center light" data-outlet="input-unit-lbl"><span>Unit</span></div>
                <div class="label align-right light" data-outlet="product-quantity-lbl"><span>0</span></div>
                <div class="blank"></div>
                <div class="label align-right light" data-outlet="product-gross-price-lbl"><span>0</span></div>
                <div class="label align-right light" data-outlet="discount-amount-lbl"><span>0</span></div>
                <div class="blank"></div>
            </div>

            <!--new cell-->
            <div class="cell" data-cell-identifier="LineCell" data-class="SupplierNoteLineCell">
                <!--first cell line-->
                <div class="btn remove" data-editing-accessory-view="delete"></div>
                <div class="row col-span-2">
                    <div class="label" data-outlet="product-lbl">Concept name</div>
                    <div class="btn info-cell" data-outlet="info-icon-btn"></div>
                </div>
                <div class="label align-right" data-outlet="input-format-lbl"></div>
                <div class="label align-right" data-outlet="quantity-lbl"></div>
                <div class="label align-right" data-outlet="price-lbl"></div>
                <div class="label align-right" data-outlet="product-price-lbl"></div>
                <div class="label align-right" data-outlet="discount-lbl"></div>
                <div class="label align-right" data-outlet="total-lbl"></div>
                <!--second cell line-->
                <div class="blank"></div>
                <div class="label light" data-outlet="concept-subtext-lbl">Concept label</div>
                <div class="blank"></div>
                <div class="label align-right light" data-outlet="input-unit-lbl"><span>Unit</span></div>
                <div class="label align-right light" data-outlet="product-quantity-lbl"><span>Unit</span></div>
                <div class="blank"></div>
                <div class="label align-right light" data-outlet="product-gross-price-lbl"><span>0</span></div>
                <div class="label align-right light" data-outlet="discount-amount-lbl"><span>Unit</span></div>
                <div class="blank"></div>
            </div>
        
        </div>

        <div class="footer-container dark">
          
                <div class="grid-row add-line" data-outlet="bottom-add-line" data-cell-identifier="AddLineCell" data-class="SupplierNoteLineCell">
                    <div class="btn add white" data-outlet="add-line-btn"></div>
                    <div class="input-text col-span-2 align-left" data-outlet="product-tf">
                        <input type="text" placeholder="CONCEPT NAME" data-placeholder="CONCEPT NAME">
                    </div>      
                    <div class="btn dropdown align-center" data-outlet="input-format-dd"><span data-localized-key="INPUT_FORMAT">INPUT FORMAT</span></div>
                    <div class="input-text align-right" data-outlet="quantity-tf">
                        <input type="text" placeholder="0" data-placeholder="0">
                    </div>
                    <div class="input-text align-right" data-outlet="price-tf">
                        <input type="text" placeholder="00.00" data-placeholder="00.00">
                    </div>
                    <div class="input-text align-right" data-outlet="product-price-tf">
                        <input type="text" placeholder="00.00" data-placeholder="00.00">
                    </div>
                    <div class="input-text align-right" data-outlet="discount-tf">
                        <input type="text" placeholder="0" data-placeholder="0">
                    </div>
                    <div class="input-text align-right" data-outlet="total-tf">
                        <input type="text" placeholder="00.00" data-placeholder="00.00">
                    </div>
                </div>
          
        </div>
        
    </div>

</body>
</html>
