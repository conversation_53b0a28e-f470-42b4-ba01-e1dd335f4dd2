<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>DeliveryNoteDetailView</title>
    <link href="../../styles.css" rel="stylesheet" />
</head>

<body>
    <!-- Page -->
    <div id="delivery-note-detail-view" class="page">

        <!--toolbar-->
        <div class="toolbar">
            <div class="left"></div>
            <div class="center">
                <div class="title">
                    <span data-localized-key="DELIVERY_NOTE">DELIVERY NOTE</span>
                </div>
            </div>
            <div class="right">
                <div class="btn dropdown hidden" data-outlet="select-category"><span data-localized-key="SELECT_CATEGORY">SELECT CATEGORY</span></div>
                <div class="btn dropdown hidden" data-outlet="select-tab"><span data-localized-key="SELECT_TAG">SELECT TAG</span></div>
                <div class="btn save" data-outlet="save-btn"><span data-localized-key="SAVE">SAVE</span></div>
                <div class="btn settings" data-outlet="action-btn"></div>
            </div>
        </div>

       
        <!--header-container-->
        <div class="header-container" data-outlet="no-edit-header-view">
            <div class="box">
                <div class="title"><span data-localized-key="DOCUMENT_ID">DOCUMENT ID</span></div>
                <div class="label" data-outlet="document-id-lbl"><span>Label</span></div>
            </div>
            <div class="box">
                <div class="title"><span data-localized-key="DESTINATION_NAME">DESTINATION NAME</span></div>
                <div class="label" data-outlet="destination-lbl"><span>Label</span></div>
            </div>
            <div class="box">
                <div class="title"><span data-localized-key="EXTERNAL_REFERENCE">EXTERNAL REFERENCE</span></div>
                <div class="label" data-outlet="external-reference-lbl"><span>Label</span></div>
            </div>
            <div class="box">
                <div class="title"><span data-localized-key="DOCUMENT_DATE">DOCUMENT DATE</span></div>
                <div class="label" data-outlet="document-date-lbl"><span>Label</span></div>
            </div>
            <div class="box">
                <div class="title"><span data-localized-key="VALIDATION_DATE">VALIDATION DATE</span></div>
                <div class="label" data-outlet="stock-date-lbl"><span>Label</span></div>
            </div>
            <div class="box">
                <div class="blank"></div>
                <div class="btn alert" data-outlet="change-status-btn"><span data-localized-key="PROCESSED">PROCESSED</span></div>
            </div>
        </div>

        <!--header-container-->
        <div class="header-container" data-outlet="edit-header-view">
            <div class="box">
                <div class="title"><span data-localized-key="DOCUMENT_ID">DOCUMENT ID</span></div>
                <div class="label" data-outlet="document-id-lbl"><span>Label</span></div>
            </div>
            <div class="box">
                <div class="title"><span data-localized-key="DESTINATION_NAME">DESTINATION NAME</span></div>
                <div class="label" data-outlet="destination-lbl"><span>Label</span></div>
            </div>
            <div class="box">
                <div class="title"><span dada-localized-key="EXTERNAL REFERENCE">EXTERNAL REFERENCE</span></div>
                <div class="input-text" data-outlet="external-reference-tf">
                    <input type="text" placeholder="EXTERNAL REFERENCE" data-placeholder="EXTERNAL REFERENCE">
                </div>
            </div>
            <div class="box">
                    <div class="title"><span data-localized-key="DOCUMENT_DATE">DOCUMENT DATE</span></div>
                <div class="input-btn" data-outlet="document-date-tf">
                    <input type="text" placeholder="DD/MM/YYYY" data-placeholder="DD/MM/YYYY">
                    <div class="btn calendar" data-outlet="document-date-btn"></div>
                </div>
            </div>
            <div class="box">
                <div class="title"><span data-localized-key="VALIDATION_DATE">VALIDATION DATE</span></div>
                <div class="input-btn" data-outlet="stock-date-tf">
                    <input type="text" placeholder="DD/MM/YYYY" data-placeholder="DD/MM/YYYY">
                    <div class="btn calendar" data-outlet="stock-date-btn"></div>
                </div>
            </div>
            <div class="box">
                <div class="blank"></div>
                <div class="btn" data-outlet="change-status-btn"><span data-localized-key="PROCESS">PROCESS</span></div>
            </div>
        </div>

        <!--header-container-->
        <div class="header-container">
            <div class="box col-span-4">
                <div class="title"><span data-localized-key="COMMENTS">COMMENTS</span></div>
                <div class="input-text align-left" data-outlet="comments-tf">
                    <input type="text" placeholder="COMMENTS" data-placeholder="COMMENTS">
                </div>
            </div>
            <div class="box col-span-2">
                <div class="title"><span data-localized-key="TOTAL">TOTAL</span></div>
                <div class="label strong large" data-outlet="total-lbl"><span>Label</span></div>
            </div>
        </div>   

        <!--searchbar-->
        <div class="searchbar" data-outlet="search-bar">
            <input type="search" placeholder="SEARCH" data-placeholder="SEARCH">
        </div>

        <!--titlebar-->
        <div class="titlebar">
            <div class="icon-spacer"></div>
            <div class="title"><span data-localized-key="CONCEPT">CONCEPT</span></div>            
            <div class="title align-center"><span data-localized-key="TAX">TAX</span></div>
            <div class="title align-center"><span data-localized-key="WAREHOUSE">WAREHOUSE</span></div>            
            <div class="title align-center"><span data-localized-key="ORDERED_QUANTITY">ORDER QUANTITY</span></div>
            <div class="title align-center"><span data-localized-key="INPUT_FORMAT">INPUT FORMAT</span></div>
            <div class="title align-right"><span data-localized-key="QUANTITY">QUANTITY</span></div>
            <div class="title align-right"><span data-localized-key="PRICE">PRICE</span></div>
            <div class="title align-right"><span data-localized-key="PRODUCT_QUANTITY">PRODUCT QUANTITY</span></div>
            <div class="title align-right"><span data-localized-key="PRODUCT_PRICE">PRODUCT PRICE</span></div>
            <div class="title align-right"><span data-localized-key="DISCOUNT">DISCOUNT</span></div>
            <div class="title align-right"><span data-localized-key="TOTAL">TOTAL</span></div>
        </div>

        <!--content-container-->
        <div class="content-container">
            <!--top add line-->
            <div class="cell add-line" data-outlet="top-add-line" data-cell-identifier="AddLineCell" data-class="DeliveryNoteLineCell">
                <div class="btn add white"></div>
                <div class="input-text align-left" data-outlet="product-tf">
                    <input type="text" placeholder="NAME" data-placeholder="NAME">
                </div>                
                <div class="btn dropdown align-center" data-outlet="product-tax-dd"><span data-localized-key="TAX">TAX</span></div>
                <div class="btn dropdown align-center" data-outlet="warehouse-dd"><span data-localized-key="WAREHOUSE">WAREHOUSE</span></div>
                <div class="label align-center" data-outlet="ordered-quantity-lbl"><span>0</span></div>
                <div class="btn dropdown align-center" data-outlet="input-format-dd"><span data-localized-key="INPUT_FORMAT">INPUT FORMAT</span></div>

                <div class="input-text align-right" data-outlet="quantity-tf">
                    <input type="text" placeholder="0" data-placeholder="">
                </div>
                <div class="input-text align-right" data-outlet="price-tf">
                    <input type="text" placeholder="0" data-placeholder="">
                </div>

                <div class="label align-right" data-outlet="product-quantity-lbl"><span>Total</span></div>
                <div class="label align-right" data-outlet="product-price-lbl"><span>Total</span></div>
                <div class="input-text align-right" data-outlet="discount-tf">
                    <input type="text" placeholder="0" data-placeholder="">
                </div>
                <div class="label strong align-right" data-outlet="total-price-lbl"><span>Total</span></div>
            </div>
        </div>

        <!--table-->
        <div class="table" data-outlet="table-view">
            
            <!--cell-->
            

            <!--cell-->
            <div class="cell" data-cell-identifier="EditLineCell" data-class="DeliveryNoteLineCell">
                <div class="btn remove" data-editing-accessory-view="delete"></div>
                <div class="label" data-outlet="product-lbl"><span>Label</span></div>                
                <div class="btn dropdown align-center" data-outlet="product-tax-dd"><span data-localized-key="TAX">TAX</span></div>
                <div class="btn dropdown align-center" data-outlet="warehouse-dd"><span data-localized-key="WAREHOUSE">WAREHOUSE</span></div>
                <div class="label align-center" data-outlet="ordered-quantity-lbl"><span>0</span></div>
                <div class="btn dropdown align-center" data-outlet="input-format-dd"><span data-localized-key="INPUT_FORMAT">Tax</span></div>
                
                <div class="input-text align-right" data-outlet="quantity-tf">
                    <input type="text" placeholder="0" data-placeholder="">
                </div>                
                <div class="input-text align-right" data-outlet="price-tf">
                    <input type="text" placeholder="0" data-placeholder="">
                </div>
                <div class="label align-right" data-outlet="product-quantity-lbl"><span>Total</span></div>
                <div class="label align-right" data-outlet="product-price-lbl"><span>Total</span></div>
                <div class="input-text align-right" data-outlet="discount-tf">
                    <input type="text" placeholder="0" data-placeholder="">
                </div>
                <div class="label strong align-right" data-outlet="total-price-lbl"><span>Total</span></div>
            </div>

            <!--cell-->
            <div class="cell" data-cell-identifier="LineCell" data-class="DeliveryNoteLineCell">
                <div class="icon-spacer"></div>
                <div class="label" data-outlet="product-lbl"><span>Label</span></div>                
                <div class="label align-center" data-outlet="product-tax-lbl"><span>Label</span></div>
                <div class="label align-center" data-outlet="warehouse-lbl"><span>Label</span></div>
                <div class="label align-center" data-outlet="ordered-quantity-lbl"><span>0</span></div>
                <div class="label align-center" data-outlet="input-format-lbl"><span></span></div>
                <div class="label align-right" data-outlet="quantity-lbl"><span>Label</span></div>                
                <div class="label align-right" data-outlet="price-lbl"><span>Label</span></div>
                <div class="label align-right" data-outlet="product-quantity-lbl"><span>Total</span></div>
                <div class="label align-right" data-outlet="product-price-lbl"><span>Total</span></div>
                <div class="label align-right" data-outlet="discount-lbl"><span>Label</span></div>
                <div class="label strong align-right" data-outlet="total-price-lbl"><span>Total</span></div>
            </div>
        </div>

        <div class="footer-container dark">
            <!--bottom add line-->
            <div class="cell add-line" data-outlet="bottom-add-line" data-cell-identifier="AddLineCell" data-class="DeliveryNoteLineCell">
                <div class="btn add white"></div>
                <div class="input-text align-left" data-outlet="product-tf">
                    <input type="text" placeholder="Name" data-placeholder="NAME">
                </div>                
                <div class="btn dropdown align-center" data-outlet="product-tax-dd"><span data-localized-key="TAX">Tax</span></div>
                <div class="btn dropdown align-center" data-outlet="warehouse-dd"><span data-localized-key="WAREHOUSE">Warehouse</span></div>
                <div class="label align-center" data-outlet="ordered-quantity-lbl"><span>0</span></div>
                <div class="btn dropdown align-center" data-outlet="input-format-dd"><span data-localized-key="INPUT_FORMAT">INPUT FORMAT</span></div>

                <div class="input-text align-right" data-outlet="quantity-tf">
                    <input type="text" placeholder="0" data-placeholder="">
                </div>
                <div class="input-text align-right" data-outlet="price-tf">
                    <input type="text" placeholder="0" data-placeholder="">
                </div>

                <div class="label align-right" data-outlet="product-quantity-lbl"><span>Total</span></div>
                <div class="label align-right" data-outlet="product-price-lbl"><span>Total</span></div>
                <div class="input-text align-right" data-outlet="discount-tf">
                    <input type="text" placeholder="0" data-placeholder="">
                </div>
                <div class="label strong align-right" data-outlet="total-price-lbl"><span>Total</span></div>
            </div>
        </div>

    </div>

</body>

</html>
