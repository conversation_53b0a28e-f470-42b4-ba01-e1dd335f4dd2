<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="user-scalable=0, initial-scale=1.0">
    <title>ReportDetailView</title>
    <link href="../../styles.css" rel="stylesheet" />
</head>
<body>
    <!-- page -->
    <div id="report-detail-view" class="page">

        <!--toolbar-->
        <div class="toolbar" data-outlet="nav-bar">
            <div class="left">
                <div class="btn menu" data-outlet="sections-btn"></div>
                <div class="btn back" data-outlet="back-btn"><span data-localized-key="BACK">Back</span></div>
                <div class="btn back" data-outlet="sv-back-btn"><span data-localized-key="BACK">Back</span></div>
                <div class="btn pin hidden" data-outlet="btn-pin"></div>
                <div class="btn unpin hidden" data-outlet="btn-unpin"></div>
            </div>
            <div class="center">
                <div class="title" data-outlet="title-lbl"><span data-localized-key="REPORTS">Reports</span></div>
                <div class="label" data-outlet="date-lbl">00/00/0000</div>
            </div>
            <div class="right">
                <div class="btn" data-outlet="ai-btn" id="ai-button"><span data-localized-key="AI CHAT">AI CHAT</span></div>
                <div class="blank"></div>
                <div class="btn refresh" data-outlet="refresh-btn"></div>
                <div class="btn filter-off" data-outlet="filter-btn"></div>
                <div class="btn vieweye" data-outlet="visibility-btn"></div>
                <div class="btn export" data-outlet="export-btn"></div>
            </div>
        </div>

        <div class="flex-container">

            <!--left container-->
            <!--ADD "display:none;" TO THE "left-container" TO HIDE THE CONTAINER-->
            <div class="left-container" style="display: none;" data-outlet="left-view"></div>  
    
            <!--main content container for the page, center view-->
            <div class="main-container" data-outlet="results-view">

                <!-- Display contents of tables here -->
                <div class="data-table-content" data-table-content="true"></div>

                <!--LOADING DIV (covers other divs)-->
                <div class="loading-screen" data-outlet="loading-view">
                    <!--spinner-->
                    <div class="spin-loader" data-outlet="loader"></div>
                    <div class="label light large" data-outlet="loading-lbl"><span data-localized-key="LOADING_REPORT...">Loading report</span></div>
                </div>

                <!-- ROW - container for each row (in python begin column/end column)-->
                <div class="row-container" data-layout-column="true"></div>

                <!--CHART CONTAINER - contains charts/graphics (if multiple all space is evenly divided)-->
                <div class="chart-container" data-section-chart="true">
                    <div class="dashboard-content">
                        <div class="title align-center" data-outlet="chart-title-lbl"></div>
                        <div class="chart" data-outlet="chart-view"></div>
                    </div>
                </div>   

                <!-- REPORT BOX - box outline with title and label for report items.-->
                <div class="report-box" data-section-header="true">
                    <div class="title" data-outlet="header-title-lbl"><span>Title</span></div>
                    <div class="label" data-outlet="header-value-lbl"><span>Label</span></div>
                </div>

                <!--TABLE DISPLAY - report table container - used for creating comparison tables- simular to html tables -->
                <div class="table-display" data-section-table="true">
                    <div class="tr title" data-section-table-title="true">
                        <div class="title" data-outlet="title-lbl"><span>title here</span></div>
                    </div>
                    <div class="tr header" data-section-table-header-row="true"></div>
                    <div class="td header" data-section-table-header-column="true"></div>
                    <div class="td report" data-section-table-header-column-filter="true">
                        
                        <div class="input-text column-sorter" data-outlet="filter-tf">
                            <input type="text" placeholder="FILTER" data-placeholder="FILTER">
                            <div class="btn order-sort" data-outlet="sort-btn"></div>
                        </div>

                        <!--new hover filter item-->
                        <!-- <div class="column-sorter">
                          <input type="text" placeholder="Filter" required>
                        </div> -->

                    </div>
                    <div class="tr" data-section-table-row="true"></div>
                    <div class="tr sub" data-section-table-subrow="true"></div>
                    <div class="td" data-section-table-row-cell="true">Label</div>                
                    <div class="tr footer" data-section-table-footer-row="true"></div>
                </div>

            </div>

            <!--filter-container-right-->
            <!--ADD "display:none;" TO THE "right-container" TO HIDE THE CONTAINER-->
            <div class="right-container" data-outlet="right-view"></div>            

        </div>
        
    </div>
</body>
</html>