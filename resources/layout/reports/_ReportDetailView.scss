#report-detail-view {

    &.phone .chart-container { width: 95%; }
    &.phone .table-display { width: 95%; }

    .toolbar {
        .center { flex-direction: column; }
    }

    .flex-container {
        display: flex;
        position: relative;
        flex-direction: row;
        width: 100%;
        height: 100%;
    }

    .left-container {
        position: relative;
        width: 320px;
        height: 100%;
        border-right: 1px solid $grey-border;
    }

    .right-container {
        position: relative;
        width: 320px;
        height: 100%;
        border-left: 1px solid $grey-border;
    }

    .main-container {
        background-color: $white;
        width: 100%;
        height: 100%;
        // updated for scroll on window not on table
        // position: relative;
        position: absolute;
        top: 0;
        left: 0;

        overflow-x: auto;
        overflow-y: auto;
        scrollbar-color: $grey-border $grey-background;
        scrollbar-width: thin;
        scrollbar-height: thin;

        &::-webkit-scrollbar  {
            width: 8px;
            height: 8px;
            background-color: $grey-background;
        }

        &::-webkit-scrollbar-button  {
            display: none;
        }

        &::-webkit-scrollbar-thumb  {
            background-color: $grey-border;
        }
    }

    .data-table-content {
        position: relative;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        // width: 100%;
        
        // updated for scroll on window not on table
        width: fit-content;
    }

    .row-container {
        position: relative;
        flex-direction: row;
        justify-content: flex-start;
        align-items: flex-start;
        padding: 8px 8px 16px 8px;
        box-sizing: border-box;

        // updated for scroll on window not on table
        display: inline-flex;
        width: fit-content;
        min-width: 100vw;

        >:first-child {
            &.report-box { margin-left: auto; }
        }

        >:last-child {
            &.report-box { margin-right: auto; }
        }

        overflow-x: auto;
        scrollbar-color: $grey-border $grey-background;
        scrollbar-width: thin;
        scrollbar-height: thin;

        &::-webkit-scrollbar  {
            width: 8px;
            height: 8px;
            background-color: $grey-background;
        }

        &::-webkit-scrollbar-button  {
            display: none;
        }

        &::-webkit-scrollbar-thumb  {
            background-color: $grey-border;
        }

    }

    .report-box {
        display: block;
        position: relative;
        padding: 8px;
        margin: 8px;
        border-radius: 15px;
        border: 1px solid #D0D0DB;
        box-sizing: border-box;

        .title {
            text-align: center;
            color: #00bebe;
            font-weight: bold;
            font-family: bold;
            font-size: 1.2em;
            text-transform: uppercase;
            white-space: nowrap;
            // border-bottom: 2px solid #00bebe;
        }

        .label {
            text-align: center;
            font-weight: bold;
            font-family: bold;
            font-size: 170%;
            white-space: nowrap;
        }
    }

    .report-img-box {

        .title {
            color: #00bebe;
            font-weight: bold;
            font-family: bold;
            font-size: 1.2em;
            text-transform: uppercase;
        }

        .report-img {
            background-color: #dddddd;
            width: 100%;
            height: 100px;
        }
    }

    .chart-container {
        position: relative;
        padding: 8px;
        margin: 8px;
        border-radius: 15px;
        border: 1px solid #D0D0DB;
        box-sizing: border-box;

        align-self: stretch;
        flex-shrink: 1;
        flex-grow: 1;
        flex-basis: 100%;
        min-width: 100px;

        .dashboard-content {
            height: 100%;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .title {
            text-align: center;
            color: #00bebe;
            font-weight: bold;
            font-family: bold;
            font-size: 1.2em;
            text-transform: uppercase;
            // border-bottom: 2px solid #00bebe;
        }

        .chart {
            position: relative;
            max-width: 95vw;
        }
    }

    .table-display {
        margin: 16px 8px 8px 8px;
        align-self: flex-start;
    }

    .loading-screen {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;

        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .spin-loader {
            height: 40px;
            width: 40px;
            margin-bottom: 1em;
        }
    }

     .ai-button {
      position: relative;
      padding: 12px 28px;
      font-size: 20px;
      font-weight: bold;
      color: white;
      background: #0f0f0f;
      border: none;
      border-radius: 12px;
      cursor: pointer;
      z-index: 1;
      overflow: hidden;
    }

    .ai-button::before {
      content: "";
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(90deg, #ff00ff, #00ffff, #ff9900, #00ff00, #ff00ff);
      background-size: 400%;
      border-radius: 14px;
      z-index: -1;
      animation: borderflow 5s linear infinite;
    }

    .ai-button::after {
      content: "";
      position: absolute;
      inset: 2px;
      background: #0f0f0f;
      border-radius: 10px;
      z-index: -1;
    }

    @keyframes borderflow {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    .ai-button:hover {
      color: #00ffff;
      text-shadow: 0 0 8px #00ffff;
    }
}
