<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>UseNewServerConfigFile</key>
	<false/>

	<key>IgnoreFlatResult</key>
	<false/>
 
	<key>IgnoreServerAuthURLs</key>
	<false/>

	<key>AuthServerURL</key> 
	<string>https://auth.dual-link.com</string>
	<!-- <string>http://localhost:8080</string> -->
	<!-- <string>http://**********:8080</string> -->

	<key>EntityServerURL</key>	
	<string>http://localhost:8103/entity</string>
	<!-- <string>http://**********:8103/entity</string> -->
	<!-- <string>https://test.dual-link.com/entity</string> -->
	
	<key>APIServerURL</key>
	<!-- <string>https://dev2.dual-link.com/api</string> -->
	<!-- <string>https://v62-es-aws.dual-link.com/api</string> -->
	<!-- <string>https://v62-staging-hq.dual-link.com/api</string>  -->
	<!-- <string>https://hq.dual-link.com/api</string> -->
	<!-- <string>https://layali.dual-link.com/api</string> -->
	<!-- <string>http://**********:8090/api</string> -->
	<string>http://localhost:8090/api</string>
	<!-- <string>https://staging.dual-link.com/api</string> -->

	<key>SyncServerURL</key>
	<!-- <string>https://dev2.dual-link.com/sync</string> -->
	<!-- <string>https://test.dual-link.com/sync</string> -->
	<string>http://localhost:8091/sync</string>
	<!-- <string>http://*********:8091/sync</string> -->
	<!-- <string>https://v62-es-aws.dual-link.com/sync</string> -->
	<!-- <string>https://v63-staging-aws.dual-link.com/sync</string> -->
	<!-- <string>https://staging.dual-link.com/sync</string> -->


	<key>PushServerURL</key>
	<!-- <string>https://dev2.dual-link.com/push</string> -->
	<!-- <string>https://test.dual-link.com/push</string> -->
	<!-- <string>http://localhost:8092/push</string> -->
	<!-- <string>https://v63-staging-aws.dual-link.com/push</string> -->
	<string>https://staging.dual-link.com/push</string>


	<key>ScriptServerURL</key>
	<!-- <string>https://dev2.dual-link.com/script</string> -->
	<!-- <string>https://test.dual-link.com/script</string> -->
	<string>http://localhost:8094/script</string>
	<!-- <string>http://**********:8094/script</string> -->
	<!-- <string>https://v62-staging-aws.dual-link.com/script</string> -->
	<!-- <string>https://staging.dual-link.com/script</string> -->

	<key>TemplateServerURL</key>
	<!-- <string>https://dev2.dual-link.com/template</string> -->
	<!-- <string>https://test.dual-link.com/template</string> -->
	<string>http://localhost:8096/template</string>
	<!-- <string>https://staging.dual-link.com/template</string> -->

	<key>IntegratorServerURL</key>
	<!-- <string>https://dev2.dual-link.com/integrator</string> -->
	<!-- <string>https://test.dual-link.com/integrator</string> -->
	<string>http://localhost:8095/integrator</string>
	<!-- <string>https://v62-es-aws.dual-link.com/integrator</string> -->
	<!-- <string>https://staging.dual-link.com/integrator</string> -->

	<key>ToolServerURL</key>
	<!-- <string>https://dev2.dual-link.com/tool</string> -->
	<!-- <string>https://test.dual-link.com/tool</string> -->
	<!-- <string>http://localhost:8099/tool</string> -->
	<!-- <string>https://v62-es-aws.dual-link.com/tool</string> -->
	<string>https://staging.dual-link.com/tool</string>

	<key>PaymentServerURL</key>	
	<!-- <string>https://payment.dual-link.com</string> -->
	<string>http://localhost:8080</string>

	<key>FileServerURL</key>		
	<string>http://localhost:8101/file</string>

	<key>StockServerURL</key>		
	<string>http://localhost:8097/stock</string>

	<key>InsightsServerURL</key>
	<string>https://test.dual-link.com/insights-bot</string>

	<key>UseNewSync</key>
	<true/>

</dict>
</plist>
