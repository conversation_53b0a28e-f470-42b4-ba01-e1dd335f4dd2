/// <reference path="WebService.ts" />

interface WebService
{
    transferFunds( installment:PaymentInstallment, completion:any ) : void ;
}

WebService.prototype.transferFunds = function( installment:PaymentInstallment, completion:any ) : void
{
    const url_str = this.paymentURL + "/adyen/schema/" + this.scheme + "/transfer-funds/" + installment.identifier;
    let r = new DLRequest ( url_str, null, "GET" );
    r.execute( completion );
}
