// Type alias for the completion callback function
type ConversationCompletionCallback = (result: any, error: string) => void;

interface WebService
{
    newConversation( completion: ConversationCompletionCallback ) : void;
}

WebService.prototype.newConversation = function( completion: ConversationCompletionCallback ) {
    const url_str = this.insightsURL + "/schema/" + this.scheme + "/conversation";
    let r = new DLRequest ( url_str, {}, "POST" );
    r.execute( (json:any, error:string) => {
        completion(json["conversation"], error);
    } );
}
