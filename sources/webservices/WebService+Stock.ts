interface WebService
{
    currentStock( products:string[], completion:any ) : void ;
    updateProductCost( product:Product, completion:any) : void ;
    createInventoryNoteFromTemplate( warehouse:Warehouse, template:StockTemplate, completion:any ) : void ;
    canProcessStockNote ( stockNote:StockNote, completion:any ) : void ;
}

WebService.prototype.currentStock = function( products:string[], completion:any ) : void
{
    const url_str = this.scriptURL + "/schema/" + this.scheme + "/python/1_stock_current_summary.py";
    let body = { 
        "filters" : {
            "__report_output_type__":1,
            "session_date": "2025-04-29 00:00:00,2025-04-29 23:59:59",
            "products": products
        }
    };
    let r = new MWSJSONRequest();
    r.initWithURL ( MIOURL.urlWithString( url_str ), body, "POST" );
    r.execute( this, (code:number,data:any) => completion(data, code == 200 ? null : data) );
}

WebService.prototype.updateProductCost = function( product:Product, completion:any ) : void
{
    const url_str = this.stockURL + "/schema/" + this.scheme + "/update-product-cost/" + product.identifier;
    let body = { "CostType": product.costType };
    if (product.costProductPrice != null) {
        body["CostProductPrice"] = product.costProductPrice;
     }
    let r = new DLRequest ( url_str, body, "POST" );
    r.execute( completion );
}

WebService.prototype.createInventoryNoteFromTemplate = function( warehouse:Warehouse, template:StockTemplate, completion:any ) 
{
    const url = this.stockURL + "/schema/" + this.scheme + "/InventoryNote/create/" + warehouse.identifier + (template != null ? "/" + template.identifier : "" );
    const r = new DLRequest(url);
    r.execute( (json:any, error:string) => {
        if (error != null) { 
            AppHelper.showErrorMessage(null, MIOLocalizeString("ERROR_CREATING_INVENTORY", "ERROR CREATING INVENTORY") , error);
            return;
        }

        let noteID = json["stockNoteID"];
        DBHelper.queryObjectWithCompletion( "StockNote", null, MIOPredicate.predicateWithFormat( "identifier = " + noteID ), [], this, function( object:InventoryNote) {
            completion(object);
        });
    } );   
}

WebService.prototype.canProcessStockNote = function( stockNote:StockNote, completion:any )
{
    let ad = MUIWebApplication.sharedInstance().delegate as AppDelegate;

    let body = {}
    body["date"] = ad.serverDateTimeFormatter.stringFromDate( stockNote.stockDate );
    body["products"] = stockNote.lines.map( (line:StockNoteProductLine) => line.product.identifier )

    const url_str = this.stockURL + this.stockURL + "/entity/" + this.scheme + "/InventoryNote/can-process-note/";
    const r = new DLRequest(url_str, body, "POST");
    r.execute( completion );
}


