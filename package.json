{"name": "dlwebmanager", "version": "4.62.872", "description": "Dual-Link webmanager", "main": "gulpfile.js", "dependencies": {"@types/base64-js": "^1.3.0", "audio-recorder-polyfill": "^0.3.6", "base64-js": "^1.5.1", "brace": "^0.11.1", "jspdf": "^1.5.3", "miojslibs": "file:../../Libs/MIOJSLibs/dist", "monaco-editor": "^0.26.1", "uuid": "^11.1.0"}, "engines": {"node": ">8.5.0"}, "devDependencies": {"@types/ace": "0.0.42", "@types/googlemaps": "^3.38.1", "@types/jspdf": "^1.3.3", "@types/uuid": "^10.0.0", "ajv-errors": "^3.0.0", "ansi-colors": "^1.1.0", "browser-sync": "^2.26.7", "colors": "^1.4.0", "compass-importer": "^0.4.1", "copyfiles": "^2.1.1", "cross-env": "^5.2.1", "fs": "0.0.1-security", "glob": "^11.0.2", "gulp": "^3.9.1", "gulp-clean-css": "^3.10.0", "gulp-cli": "^2.3.0", "gulp-compass": "^2.1.0", "gulp-cssbeautify": "^1.0.1", "gulp-debug": "^3.2.0", "gulp-flatten": "^0.4.0", "gulp-htmlhint": "^2.1.0", "gulp-inject": "^4.3.1", "gulp-inject-version": "^1.0.1", "gulp-natural-sort": "^0.1.1", "gulp-newer": "^1.4.0", "gulp-rename": "^1.4.0", "gulp-run-command": "0.0.9", "gulp-sass": "^5.1.55", "gulp-sourcemaps": "^1.12.1", "gulp-tslint": "^8.1.4", "gulp-typescript": "^3.2.4", "gulp-watch": "^5.0.1", "gulp-zip": "^4.2.0", "htmlhint": "^0.9.13", "keys-diff": "^1.0.7", "merge2": "^1.3.0", "npm": "^6.13.4", "pretty-hrtime": "^1.0.3", "sass": "^1.55.0", "sass-lint": "^1.13.1", "streamqueue": "^1.1.2", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "typescript": "^3.7.3", "vinyl-fs": "^3.0.2", "webpack": "^4.41.3", "webpack-cli": "^3.3.10"}, "scripts": {"clear": "npm run clear:app && npm run clear:libs", "clear:app": "rimraf app/*", "clear:libs": "rimraf resources/libs/miojslibs/*", "tsc": "tsc -w -p .", "predev": "npm run clear:libs", "dev": "gulp dev", "update-libs": "cd ../../Libs/MIOJSLibs && git stash push -u -m 'update-libs-temp' && git checkout master && git pull && npm install && git stash pop", "htmlhint": "htmlhint", "tslint": "tslint --project .", "lint": "npm run htmlhint && npm run tslint", "test": "echo \\\"Error: no test specified\\\" && exit 1", "prebundle-aws": "npm run lint", "bundle-aws": "gulp bundle:aws", "preprod-aws": "npm install && npm run lint && npm run clear", "prod-aws": "gulp prod-aws", "link:libs": "cd ../../Libs/MIOJSLibs && npm link && cd packages/miojslibs && npm link", "postlink:libs": "npm link miojslibs-wrapper && npm link miojslibs", "unlink:libs": "npm unlink miojslibs-wrapper && npm unlink miojslibs", "dev:libs": "gulp dev:libs", "build:libs": "cd node_modules/miojslibs-wrapper/packages/miojslibs && cross-env NODE_ENV=dev npm run dev", "update-libs:dev": "cd ../../Libs/MIOJSLibs && git stash push -u -m 'update-libs:dev' && git pull && git stash pop", "generate-map-items": "file app/images/manager-3_0/tablemap/*.png app/images/manager-3_0/tablemap/**/*.png | python utils/generate_map_items.py > ./resources/layout/event/_EventDetailMapItems.scss && echo '_EventDetailMapItems.scss file updated!'", "check-translations": "./utils/translation/generate_diffs.sh && node ./utils/translation/i18nkezdiff.js", "validate-translations": "node scripts/validate-translations.js", "check-unused-translations": "node scripts/check-translations.js", "cleanup-translation-backups": "node scripts/cleanup-translation-backups.js", "sync-translations": "node scripts/sync-translations.js"}, "repository": {"type": "git", "url": "git+https://github.com/MIOLabs/DLWebManager.git"}, "author": "", "license": "UNLICENSED", "bugs": {"url": "https://github.com/MIOLabs/DLWebManager/issues"}, "homepage": "https://github.com/MIOLabs/DLWebManager#readme", "overrides": {"graceful-fs": "^4.2.10"}}